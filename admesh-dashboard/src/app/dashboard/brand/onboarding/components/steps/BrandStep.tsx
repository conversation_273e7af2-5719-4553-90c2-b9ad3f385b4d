"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, ArrowRight, Mail, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { sendEmailVerification } from "firebase/auth";
import { useAuth } from "@/hooks/use-auth";
import { validateEmailDomainMatch, getDomainValidationError } from "@/lib/utils";
import FormField from "../ui/FormField";
import FormSection from "../ui/FormSection";
import AnimatedContainer from "../ui/AnimatedContainer";
import { Brand } from "@/types/onboarding";


interface BrandStepProps {
  brand: {
    website: string;
    brand_name: string;
    logo_url: string;
    work_email: string;
    headquarters: string;
    application_type: string;
  };
  setBrand: (brand: Brand) => void;
  errors: Record<string, string>;
  onNext: () => void;
  loading: boolean;
  userEmail: string;
  onFetchInfo: (website: string) => Promise<boolean>;
  hasCompletedBrandStep?: boolean;
  shouldShowAllFields?: boolean;
  onFieldsRevealed?: (revealed: boolean) => void;
}

const BrandStep = ({
  brand,
  setBrand,
  errors,
  onNext,
  loading,
  userEmail,
  onFetchInfo,
  hasCompletedBrandStep = false,
  shouldShowAllFields = false,
  onFieldsRevealed
}: BrandStepProps) => {
  const { user, refreshUser } = useAuth();
  const [showAllFields, setShowAllFields] = useState(shouldShowAllFields);
  const [fetchingDomainInfo, setFetchingDomainInfo] = useState(false);
  const [domainFetchError, setDomainFetchError] = useState("");
  const [sendingVerification, setSendingVerification] = useState(false);
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);
  const [hasClickedFetchInfo, setHasClickedFetchInfo] = useState(hasCompletedBrandStep);

  // Enhanced email verification state
  const [resendAttempts, setResendAttempts] = useState(0);
  const [resendCountdown, setResendCountdown] = useState(0);
  const [isCheckingVerification, setIsCheckingVerification] = useState(false);
  const [verificationCheckInterval, setVerificationCheckInterval] = useState<NodeJS.Timeout | null>(null);
  const [countdownInterval, setCountdownInterval] = useState<NodeJS.Timeout | null>(null);

  // Check if brand data is pre-filled (indicating auto-fill from existing data)
  const isDataPreFilled = brand.website || brand.brand_name || brand.work_email;

  // STATE MANAGEMENT FOR NAVIGATION: Auto-show fields when returning from later steps
  useState(() => {
    if (hasCompletedBrandStep) {
      setShowAllFields(true);
      setHasClickedFetchInfo(true);
      onFieldsRevealed?.(true);
    }
  });

  // Check if user email domain matches brand domain
  const emailMatchesDomain = validateEmailDomainMatch(userEmail, brand.website);
  const domainValidationError = getDomainValidationError(userEmail, brand.website);

  // Check if email verification is required and if user's email is verified
  const requiresEmailVerification = brand.website && !emailMatchesDomain;
  const isEmailVerified = user?.emailVerified || false;

  // Constants for email verification
  const MAX_RESEND_ATTEMPTS = 3;
  const RESEND_COOLDOWN_SECONDS = 10;
  const VERIFICATION_CHECK_INTERVAL = 2000; // 2 seconds

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      if (verificationCheckInterval) {
        clearInterval(verificationCheckInterval);
      }
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    };
  }, [verificationCheckInterval, countdownInterval]);

  // Function to check email verification status
  const checkEmailVerificationStatus = useCallback(async () => {
    if (!user) return false;

    setIsCheckingVerification(true);
    try {
      await refreshUser();
      const isVerified = user?.emailVerified || false;

      if (isVerified) {
        // Clear intervals when verification is successful
        if (verificationCheckInterval) {
          clearInterval(verificationCheckInterval);
          setVerificationCheckInterval(null);
        }
        if (countdownInterval) {
          clearInterval(countdownInterval);
          setCountdownInterval(null);
        }
        setResendCountdown(0);
        toast.success("Email verified successfully! You can now proceed to the next step.");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error checking email verification:", error);
      return false;
    } finally {
      setIsCheckingVerification(false);
    }
  }, [user, refreshUser, verificationCheckInterval, countdownInterval]);

  // Function to start countdown timer
  const startCountdownTimer = useCallback(() => {
    setResendCountdown(RESEND_COOLDOWN_SECONDS);

    const interval = setInterval(() => {
      setResendCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          setCountdownInterval(null);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    setCountdownInterval(interval);
  }, [RESEND_COOLDOWN_SECONDS]);

  // Function to start verification checking
  const startVerificationChecking = useCallback(() => {
    const interval = setInterval(async () => {
      const isVerified = await checkEmailVerificationStatus();
      if (isVerified) {
        clearInterval(interval);
        setVerificationCheckInterval(null);
      }
    }, VERIFICATION_CHECK_INTERVAL);

    setVerificationCheckInterval(interval);
  }, [checkEmailVerificationStatus, VERIFICATION_CHECK_INTERVAL]);

  const handleSendEmailVerification = async () => {
    if (!user) {
      toast.error("User not found");
      return;
    }

    // Check domain validation first
    if (domainValidationError) {
      toast.error(domainValidationError);
      return;
    }

    // Check if max attempts reached
    if (resendAttempts >= MAX_RESEND_ATTEMPTS) {
      toast.error("Maximum attempts reached. Please wait a few minutes and refresh the page to try again.");
      return;
    }

    setSendingVerification(true);
    try {
      await sendEmailVerification(user);
      setEmailVerificationSent(true);
      setResendAttempts(prev => prev + 1);

      // Start countdown timer
      startCountdownTimer();

      // Start automatic verification checking
      startVerificationChecking();

      toast.success(
        `Verification email sent! (Attempt ${resendAttempts + 1} of ${MAX_RESEND_ATTEMPTS}) Please check your inbox and click the link to verify your email.`,
        {
          duration: 8000
        }
      );
    } catch (error) {
      console.error("Error sending verification email:", error);
      toast.error("Failed to send verification email. Please try again later.");
    } finally {
      setSendingVerification(false);
    }
  };

  const handleFetchInfo = async () => {
    if (!brand.website) {
      toast.error("Please enter a website");
      return;
    }
    setFetchingDomainInfo(true);
    setDomainFetchError("");
    setHasClickedFetchInfo(true);

    try {
      // Use the parent component's fetchWebsiteInfo function
      const success = await onFetchInfo(brand.website);

      // STRICT PROGRESSIVE DISCLOSURE: Always reveal fields after clicking "Fetch Info"
      // regardless of success or failure
      setShowAllFields(true);
      onFieldsRevealed?.(true);

      if (!success) {
        // On failure, show error message but fields remain visible for manual input
        setDomainFetchError("Could not fetch information for this website. Please fill in the details manually.");
      }
    } catch (err) {
      console.error("❌ Error fetching website info:", err);
      // STRICT PROGRESSIVE DISCLOSURE: Always reveal fields after clicking "Fetch Info"
      // regardless of success or failure
      setShowAllFields(true);
      onFieldsRevealed?.(true);
      setDomainFetchError("Could not fetch information for this website. Please fill in the details manually.");
    } finally {
      setFetchingDomainInfo(false);
    }
  };

  const handleContinue = async () => {
    // COMPREHENSIVE VALIDATION: Check all requirements before proceeding

    // 1. STRICT PROGRESSIVE DISCLOSURE: Enforce mandatory "Fetch Info" interaction
    if (!hasClickedFetchInfo || !showAllFields) {
      toast.error("Please click 'Fetch Info' to load your brand details before continuing. This step is required to proceed.");
      return;
    }

    // 2. EMAIL VERIFICATION: Block progression if verification is required but not completed
    if (requiresEmailVerification && !isEmailVerified) {
      toast.error("Email verification is required to proceed. Please check your inbox and click the verification link, then refresh this page.");
      return;
    }

    // 3. DOMAIN VALIDATION: Check for any domain-related errors
    if (domainValidationError) {
      toast.error(domainValidationError);
      return;
    }

    // 4. FORM VALIDATION: Ensure all required fields are filled
    if (!brand.website || !brand.brand_name || !brand.application_type) {
      toast.error("Please fill in all required brand fields before continuing.");
      return;
    }

    // 5. EMAIL FIELD VALIDATION: If email doesn't match domain, ensure work email is provided
    if (!emailMatchesDomain && !brand.work_email) {
      toast.error("Please provide a work email for domain verification.");
      return;
    }

    // All validations passed - proceed to next step
    onNext();
  };

  return (
    <AnimatedContainer dataStep={1}>
      <FormSection
        title="Let's start with your brand"
        description="Enter your website and click 'Fetch Info' to reveal and populate your brand details. This step is required to proceed."
      >
        <div className="grid gap-6 w-full">
          <FormField>
            <Label htmlFor="website" className="text-sm font-medium">Website</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="w-full">
                <Input
                  id="website"
                  placeholder="yourbrand.com"
                  value={brand.website}
                  onChange={(e) => setBrand({ ...brand, website: e.target.value })}
                  className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.website ? 'border-red-500' : ''} ${isDataPreFilled && brand.website ? 'bg-green-50 border-green-200' : ''}`}
                />
                {errors.website && (
                  <p className="text-xs text-red-500 mt-1">{errors.website}</p>
                )}
                {isDataPreFilled && brand.website && (
                  <p className="text-xs text-green-600 mt-1">✓ Auto-filled from your account</p>
                )}
                {!showAllFields && brand.website && (
                  <p className="text-xs text-blue-600 mt-1">💡 Click &quot;Fetch Info&quot; to reveal additional brand details</p>
                )}
              </div>
              <Button
                onClick={handleFetchInfo}
                disabled={fetchingDomainInfo || loading || (!!requiresEmailVerification && !isEmailVerified)}
                variant="outline"
                className="whitespace-nowrap w-full sm:w-auto"
              >
                {fetchingDomainInfo ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Fetching...
                  </>
                ) : (
                  <>
                    <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 7V12L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Fetch Info
                  </>
                )}
              </Button>
              {/* Show verify button if email is not verified and required */}
              {requiresEmailVerification && !isEmailVerified && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSendEmailVerification}
                  disabled={sendingVerification || !!domainValidationError}
                  className="w-full sm:w-auto mt-2"
                >
                  {sendingVerification ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      {emailVerificationSent ? "Resend Verification Email" : "Send Verification Email"}
                    </>
                  )}
                </Button>
              )}
            </div>
            {domainFetchError && (
              <div className="mt-2">
                <p className="text-xs text-red-500 mb-2">{domainFetchError}</p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowAllFields(true);
                    setHasClickedFetchInfo(true);
                    onFieldsRevealed?.(true);
                  }}
                  className="text-xs"
                >
                  Fill Details Manually
                </Button>
              </div>
            )}
          </FormField>

          {/* Email Verification Section - Enhanced for prominence when required */}
          {brand.website && (
            <FormField>
              <Label className="text-sm font-medium">
                Email Verification
                {requiresEmailVerification && !isEmailVerified && (
                  <span className="ml-2 text-xs text-red-500 font-normal">• Required to proceed</span>
                )}
              </Label>
              <div className="space-y-3">
                <div className={`flex items-center gap-2 p-3 rounded-lg border ${
                  requiresEmailVerification && !isEmailVerified
                    ? 'bg-red-50 border-red-200 dark:bg-red-950/20 dark:border-red-800'
                    : 'bg-muted/30'
                }`}>
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Your email:</span>
                  <span className="text-sm font-medium">{userEmail}</span>
                  {isEmailVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-500 ml-auto" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-orange-500 ml-auto" />
                  )}
                </div>

                {emailMatchesDomain ? (
                  <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <CheckCircle className="h-4 w-4" />
                    <span>Email domain matches your website domain</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-orange-600 dark:text-orange-400">
                      <AlertCircle className="h-4 w-4" />
                      <span>Email verification required for domain validation</span>
                    </div>
                    {domainValidationError && (
                      <p className="text-xs text-red-500">{domainValidationError}</p>
                    )}
                    {!isEmailVerified && (
                      <div className="space-y-3">
                        {/* Enhanced verification button with countdown and attempt tracking */}
                        <Button
                          type="button"
                          variant={requiresEmailVerification ? "default" : "outline"}
                          size={requiresEmailVerification ? "default" : "sm"}
                          onClick={handleSendEmailVerification}
                          disabled={
                            sendingVerification ||
                            !!domainValidationError ||
                            resendCountdown > 0 ||
                            resendAttempts >= MAX_RESEND_ATTEMPTS
                          }
                          className={`w-full sm:w-auto ${
                            requiresEmailVerification
                              ? 'bg-red-600 hover:bg-red-700 text-white font-medium'
                              : ''
                          }`}
                        >
                          {sendingVerification ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Sending...
                            </>
                          ) : resendCountdown > 0 ? (
                            <>
                              <Clock className="mr-2 h-4 w-4" />
                              Resend available in {resendCountdown}s...
                            </>
                          ) : resendAttempts >= MAX_RESEND_ATTEMPTS ? (
                            <>
                              <AlertCircle className="mr-2 h-4 w-4" />
                              Max attempts reached
                            </>
                          ) : (
                            <>
                              <Mail className="mr-2 h-4 w-4" />
                              {requiresEmailVerification
                                ? (emailVerificationSent ? "Resend Verification Email" : "Verify Email")
                                : (emailVerificationSent ? "Resend Verification Email" : "Send Verification Email")
                              }
                            </>
                          )}
                        </Button>

                        {/* Status indicators */}
                        {emailVerificationSent && (
                          <div className="space-y-2">
                            {/* Attempt counter */}
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span>Attempt {resendAttempts} of {MAX_RESEND_ATTEMPTS}</span>
                              {isCheckingVerification && (
                                <>
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                  <span>Checking verification status...</span>
                                </>
                              )}
                            </div>

                            {/* Max attempts warning */}
                            {resendAttempts >= MAX_RESEND_ATTEMPTS && (
                              <div className="p-2 bg-orange-50 border border-orange-200 rounded-md dark:bg-orange-950/20 dark:border-orange-800">
                                <p className="text-xs text-orange-700 dark:text-orange-300">
                                  Maximum attempts reached. Please wait a few minutes and refresh the page to try again.
                                </p>
                              </div>
                            )}

                            {/* Countdown status */}
                            {resendCountdown > 0 && (
                              <div className="p-2 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-950/20 dark:border-blue-800">
                                <p className="text-xs text-blue-700 dark:text-blue-300">
                                  Automatically checking for email verification every 2-3 seconds...
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    {isEmailVerified && (
                      <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                        <CheckCircle className="h-4 w-4" />
                        <span>Email verified successfully</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </FormField>
          )}

          {showAllFields && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <FormField>
                <Label htmlFor="brand_name" className="text-sm font-medium">Brand Name</Label>
                <Input
                  id="brand_name"
                  placeholder="Your Brand Name"
                  value={brand.brand_name}
                  onChange={(e) => setBrand({ ...brand, brand_name: e.target.value })}
                  className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.brand_name ? 'border-red-500' : ''} ${isDataPreFilled && brand.brand_name ? 'bg-green-50 border-green-200' : ''}`}
                />
                {errors.brand_name && (
                  <p className="text-xs text-red-500 mt-1">{errors.brand_name}</p>
                )}
                {isDataPreFilled && brand.brand_name && (
                  <p className="text-xs text-green-600 mt-1">✓ Auto-filled from website information</p>
                )}
              </FormField>

              <FormField>
                <Label htmlFor="application_type" className="text-sm font-medium">Application Type</Label>
                <select
                  id="application_type"
                  value={brand.application_type}
                  onChange={(e) => setBrand({ ...brand, application_type: e.target.value })}
                  className={`w-full rounded-md border border-input bg-background p-2 focus:ring-2 focus:ring-primary/20 ${errors.application_type ? 'border-red-500' : ''}`}
                >
                  <option value="website">Website</option>
                  <option value="mobile_app">Mobile App</option>
                  <option value="desktop">Desktop Application</option>
                  <option value="both">Web & Mobile</option>
                  <option value="other">Other</option>
                </select>
                {errors.application_type ? (
                  <p className="text-xs text-red-500 mt-1">{errors.application_type}</p>
                ) : (
                  <p className="text-xs text-muted-foreground mt-1">
                    Select the platform where your product is available.
                  </p>
                )}
              </FormField>

              {/* Show work_email input ONLY if user's email doesn't match brand domain */}
              {!emailMatchesDomain && (
                <FormField>
                  <Label htmlFor="work_email" className="text-sm font-medium">Work Email</Label>
                  <div className="flex items-center w-full">
                    <Input
                      id="work_email_prefix"
                      type="text"
                      placeholder="you"
                      value={brand.work_email.split('@')[0] || ''}
                      onChange={(e) => {
                        const emailPrefix = e.target.value;
                        const cleanDomain = brand.website.replace(/^https?:\/\//, "").replace(/^www\./, "").split('/')[0];
                        setBrand({ ...brand, work_email: `${emailPrefix}@${cleanDomain}` });
                      }}
                      className={`w-2/5 sm:w-1/2 rounded-r-none transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.work_email ? 'border-red-500' : ''}`}
                    />
                    <div className="w-3/5 sm:w-1/2 flex items-center bg-muted px-2 sm:px-3 py-2 border border-l-0 border-input rounded-r-md text-muted-foreground overflow-hidden text-ellipsis whitespace-nowrap text-xs sm:text-sm">
                      @{brand.website.replace(/^https?:\/\//, "").replace(/^www\./, "").split('/')[0]}
                    </div>
                  </div>
                  {errors.work_email ? (
                    <p className="text-xs text-red-500 mt-1">{errors.work_email}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground mt-1">
                      We&apos;ll use this to verify your ownership of the domain.
                    </p>
                  )}
                </FormField>
              )}
            </motion.div>
          )}

          <motion.div
            className="flex justify-end pt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="w-full sm:w-auto"
            >
              <Button
                onClick={handleContinue}
                disabled={
                  loading ||
                  !hasClickedFetchInfo ||
                  !showAllFields ||
                  Boolean(requiresEmailVerification && !isEmailVerified)
                }
                className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="relative z-10 flex items-center justify-center">
                  {!showAllFields
                    ? "Click 'Fetch Info' First"
                    : (requiresEmailVerification && !isEmailVerified)
                      ? "Verify Email to Continue"
                      : "Next"
                  }
                  <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </FormSection>
    </AnimatedContainer>
  );
};

export default BrandStep;
